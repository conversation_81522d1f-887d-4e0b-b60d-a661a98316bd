<template>
  <view class="timeline-container">
    <view 
      class="timeline-item" 
      wx:for="{{timelineData}}" 
      wx:key="id"
      wx:for-index="index"
    >
      <!-- 左侧点线结构 -->
      <view class="timeline-left">
        <!-- 时间节点的点 -->
        <view 
          class="timeline-dot"
          style="background-color: {{item.isCurrent ? '#fa2c19' : '#fed6cf'}}"
        ></view>
        <!-- 连接线，最后一项不显示 -->
        <view 
          wx:if="{{index < timelineData.length - 1}}"
          class="timeline-line"
          style="background-color: {{item.isCurrent ? '#fed6cf' : '#fed6cf'}}"
        ></view>
      </view>
      
      <!-- 右侧内容区域 -->
      <view class="timeline-right">
        <!-- 上半部分：标题和说明 -->
        <view class="timeline-header">
          <view class="timeline-title">{{item.title}}</view>
          <view 
            wx:if="{{item.description}}"
            class="timeline-description"
          >
            {{item.description}}
          </view>
        </view>
        
        <!-- 下半部分：slot内容 -->
        <view class="timeline-content">
          <slot name="content" item="{{item}}" index="{{index}}"></slot>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';

  interface TimelineItem {
    id: string | number;
    title: string;
    description?: string;
    isCurrent?: boolean;
    [key: string]: any;
  }

  createComponent({
    properties: {
      timelineData: {
        type: Array,
        value: []
      }
    },
    data: {},
    methods: {}
  });
</script>

<style lang="scss">
  .timeline-container {
    width: 100%;
    padding: 0;
  }

  .timeline-item {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    position: relative;
    
    &:last-child {
      .timeline-left .timeline-line {
        display: none;
      }
    }
  }

  .timeline-left {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    margin-right: 24rpx;
    position: relative;
  }

  .timeline-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 4rpx; // 与标题对齐
  }

  .timeline-line {
    width: 2rpx;
    flex: 1;
    min-height: 60rpx;
    margin-top: 8rpx;
  }

  .timeline-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    min-height: 60rpx;
    padding-bottom: 32rpx;
    
    &:last-child {
      padding-bottom: 0;
    }
  }

  .timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 16rpx;
  }

  .timeline-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    line-height: 40rpx;
  }

  .timeline-description {
    font-size: 24rpx;
    color: #666;
    line-height: 32rpx;
    margin-left: 16rpx;
    flex-shrink: 0;
  }

  .timeline-content {
    width: 100%;
  }
</style>

<script type="application/json">
  {
    "component": true
  }
</script>
