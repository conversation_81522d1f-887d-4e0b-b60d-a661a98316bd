import { doRequest } from './fetch';

interface RequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | string;
  url: string;
  data?: any;
  headers?: {
    [key: string]: string;
  };
}

// Mock数据，仿照login.ts的写法
const mockRepairListResponse = {
  code: '0000',
  message: 'ok',
  data: {
    // 维修中 - 大量数据（25条）
    maintainingList: [
      {
        deviceName: 'JD40011',
        createTime: '2022-06-23 22:20',
        stationBaseId: 1234,
        stationBaseName: '北京大兴服务站',
        number: 'wx20220623222049',
        description: '雷达罩脱落',
        appointmentRepairTime: '2022-06-23 22:20',
        isInfluenceOperation: 1
      },
      {
        deviceName: 'JD40012',
        createTime: '2022-06-24 10:15',
        stationBaseId: 1235,
        stationBaseName: '上海浦东服务站',
        number: 'wx20220624101520',
        description: '传感器故障',
        appointmentRepairTime: null,
        isInfluenceOperation: 0
      },
      {
        deviceName: null,
        createTime: '2022-06-25 08:30',
        stationBaseId: 1236,
        stationBaseName: '广州天河服务站',
        number: 'wx20220625083015',
        description: undefined,
        appointmentRepairTime: '2022-06-25 09:00',
        isInfluenceOperation: 1
      },
      ...Array.from({ length: 22 }, (_, index) => ({
        deviceName: `JD400${13 + index}`,
        createTime: `2022-06-${String(26 + (index % 4)).padStart(
          2,
          '0'
        )} ${String(9 + (index % 14)).padStart(2, '0')}:${String(
          15 + (index % 45)
        ).padStart(2, '0')}`,
        stationBaseId: 1237 + index,
        stationBaseName:
          index % 5 === 0
            ? null
            : `${
                ['深圳南山', '杭州西湖', '成都锦江', '武汉江汉', '南京玄武'][
                  index % 5
                ]
              }服务站`,
        number: `wx202206${String(26 + (index % 4)).padStart(2, '0')}${String(
          9 + (index % 14)
        ).padStart(2, '0')}${String(15 + (index % 45)).padStart(
          2,
          '0'
        )}${String(30 + index).padStart(2, '0')}`,
        description:
          index % 7 === 0
            ? undefined
            : `${
                [
                  '电池故障',
                  '刹车异常',
                  '轮胎磨损',
                  '摄像头问题',
                  '雷达异常',
                  '系统故障',
                  '外观损坏'
                ][index % 7]
              }`,
        appointmentRepairTime:
          index % 3 === 0
            ? null
            : `2022-06-${String(26 + (index % 4)).padStart(2, '0')} ${String(
                10 + (index % 14)
              ).padStart(2, '0')}:00`,
        isInfluenceOperation: index % 2
      }))
    ],
    // 待验收 - 空列表
    awaitingAcceptanceList: [],
    // 验收驳回 - 少量数据（3条）
    acceptanceRejectedList: [
      {
        deviceName: 'JD40014',
        createTime: '2022-06-21 09:45',
        stationBaseId: 1237,
        stationBaseName: '深圳南山服务站',
        number: 'wx20220621094512',
        description: '摄像头模糊',
        appointmentRepairTime: '2022-06-21 11:00',
        isInfluenceOperation: 0
      },
      {
        deviceName: undefined,
        createTime: '2022-06-20 14:30',
        stationBaseId: null,
        stationBaseName: null,
        number: 'wx20220620143025',
        description: '系统重启异常',
        appointmentRepairTime: undefined,
        isInfluenceOperation: 1
      },
      {
        deviceName: 'JD40016',
        createTime: null,
        stationBaseId: 1239,
        stationBaseName: '天津滨海服务站',
        number: 'wx20220619080015',
        description: null,
        appointmentRepairTime: '2022-06-19 10:00',
        isInfluenceOperation: null
      }
    ],
    // 已完成 - 大量数据（30条）
    completedList: [
      {
        deviceName: 'JD40015',
        createTime: '2022-06-20 14:20',
        stationBaseId: 1238,
        stationBaseName: '杭州西湖服务站',
        number: 'wx20220620142035',
        description: '轮胎磨损严重',
        appointmentRepairTime: '2022-06-20 15:30',
        isInfluenceOperation: 1
      },
      {
        deviceName: 'JD40016',
        createTime: '2022-06-19 11:10',
        stationBaseId: 1239,
        stationBaseName: '成都锦江服务站',
        number: 'wx20220619111025',
        description: '刹车系统检修',
        appointmentRepairTime: '2022-06-19 13:00',
        isInfluenceOperation: 0
      },
      ...Array.from({ length: 28 }, (_, index) => ({
        deviceName: index % 4 === 0 ? null : `JD400${17 + index}`,
        createTime: `2022-06-${String(15 + (index % 5)).padStart(
          2,
          '0'
        )} ${String(8 + (index % 16)).padStart(2, '0')}:${String(
          10 + (index % 50)
        ).padStart(2, '0')}`,
        stationBaseId: index % 6 === 0 ? undefined : 1240 + index,
        stationBaseName:
          index % 8 === 0
            ? null
            : `${
                [
                  '重庆渝中',
                  '西安雁塔',
                  '长沙岳麓',
                  '合肥包河',
                  '福州鼓楼',
                  '南昌东湖',
                  '济南历下',
                  '石家庄长安'
                ][index % 8]
              }服务站`,
        number: `wx202206${String(15 + (index % 5)).padStart(2, '0')}${String(
          8 + (index % 16)
        ).padStart(2, '0')}${String(10 + (index % 50)).padStart(
          2,
          '0'
        )}${String(45 + index).padStart(2, '0')}`,
        description:
          index % 5 === 0
            ? undefined
            : `${
                ['定期保养', '紧急维修', '系统升级', '硬件更换', '外观修复'][
                  index % 5
                ]
              }`,
        appointmentRepairTime:
          index % 7 === 0
            ? null
            : `2022-06-${String(15 + (index % 5)).padStart(2, '0')} ${String(
                9 + (index % 16)
              ).padStart(2, '0')}:00`,
        isInfluenceOperation: index % 3 === 0 ? null : index % 2
      }))
    ]
  }
};

// Mock数据 - 评论列表 - 增强版本
const mockCommentListResponse = {
  code: '0000',
  message: 'ok',
  data: {
    total: 15,
    comments: [
      {
        id: 1,
        type: 'SERVICE_STATION',
        realName: '技术员张三',
        content:
          '已经检查了雷达系统，发现是雷达罩松动导致的，需要重新固定并调试参数。预计修复时间2小时。',
        createTime: '2025-07-08 13:30:00', // 今天
        attachment: [
          {
            type: 'image',
            fileKey: 'radar_check_001',
            url: 'http://rover-operation.s3.cn-north-1.jdcloud-oss.com/h88uu7vg6e.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250707T071724Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250707%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=0dc1235baff6cad1a2c697e6b220911baf0a893c36cb1faa96200214c56f7d8f'
          }
        ],
        replies: [
          {
            id: 2,
            type: 'JD',
            realName: '运营小李',
            content: '好的，请尽快处理，这辆车影响运营',
            createTime: '2025-07-08 13:35:00', // 今天
            replyToUserType: 'technician',
            replyToUserName: '张三',
            replyToRealName: '技术员张三',
            replyToCommentId: 1,
            attachment: []
          },
          {
            id: 3,
            type: 'OEM',
            realName: '站长王五',
            content: '同意，优先处理这个问题',
            createTime: '2025-07-08 13:40:00', // 今天
            replyToRealName: '运营小李',
            replyToCommentId: 1,
            attachment: [
              {
                type: 'video',
                fileKey: 'repair_video_001',
                url: 'http://rover-operation.s3.cn-north-1.jdcloud-oss.com/h88uuhlrk8.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250707T071724Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250707%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=531d961f95d305fb1931a9a8712dd4085760bccd721cbad554dde23ee876d796'
              }
            ]
          }
        ]
      },
      {
        id: 4,
        type: 'JD',
        realName: '刘娜娜',
        content:
          '我的天啊！车子突然停在路上了，听里面说什么雷达故障，现在完全不能用了',
        createTime: '2025-07-07 16:15:00', // 昨天
        attachment: [
          {
            type: 'image',
            fileKey: 'fault_scene_001',
            url: 'https://rover-operation.s3.cn-north-1.jdcloud-oss.com/1.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250707T071639Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250707%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=3656bba127993d6e8e2a616e0a6733ffa6bbb8ff4a1dfe0ec9716fb764a552de'
          },
          {
            type: 'image',
            fileKey: 'fault_scene_002',
            url: 'http://rover-operation.s3.cn-north-1.jdcloud-oss.com/h88uu7vg6e.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250707T071724Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250707%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=0dc1235baff6cad1a2c697e6b220911baf0a893c36cb1faa96200214c56f7d8f'
          }
        ],
        replies: [
          {
            id: 5,
            type: 'SERVICE_STATION',
            realName: '客服小美',
            content: '您好，我们已经收到您的反馈，正在安排技术人员前往处理',
            createTime: '2025-07-07 16:20:00', // 昨天
            replyToUserType: 'reporter',
            replyToUserName: '刘娜娜',
            replyToRealName: '刘娜娜',
            replyToCommentId: 4,
            attachment: []
          }
        ]
      },
      {
        id: 6,
        type: 'JD',
        realName: '系统消息',
        content: '维修单已创建，单号：wx20250612135920',
        createTime: '2025-07-06 14:10:00', // 前天
        attachment: [],
        replies: []
      },
      {
        id: 7,
        type: 'SERVICE_STATION',
        realName: '技术员小王',
        content: '设备已到达现场，开始检查故障原因',
        createTime: '2025-07-06 10:25:00', // 前天
        attachment: [],
        replies: [
          {
            id: 8,
            type: 'JD',
            realName: '运营经理',
            content: '收到，请及时反馈检查结果',
            createTime: '2025-07-06 10:30:00', // 前天
            replyToUserType: 'technician',
            replyToUserName: '小王',
            replyToRealName: '技术员小王',
            replyToCommentId: 7,
            attachment: []
          }
        ]
      },
      {
        id: 9,
        type: 'OEM',
        realName: '质量主管',
        content: '这个问题需要上报总部，可能涉及批次问题',
        createTime: '2025-07-05 09:45:00', // 更早（3天前）
        attachment: [
          {
            type: 'image',
            fileKey: 'quality_report_001',
            url: 'https://rover-operation.s3.cn-north-1.jdcloud-oss.com/1.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250707T071639Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250707%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=3656bba127993d6e8e2a616e0a6733ffa6bbb8ff4a1dfe0ec9716fb764a552de'
          }
        ],
        replies: []
      },
      {
        id: 10,
        type: 'JD',
        realName: '用户反馈',
        content: '车辆恢复正常了吗？我需要尽快使用',
        createTime: '2025-07-04 18:30:00', // 更早（4天前）
        attachment: [],
        replies: [
          {
            id: 11,
            type: 'SERVICE_STATION',
            realName: '客服主管',
            content: '正在加急处理中，预计明天完成维修',
            createTime: '2025-07-04 18:35:00', // 更早（4天前）
            replyToUserType: 'user',
            replyToUserName: '用户反馈',
            replyToRealName: '用户反馈',
            replyToCommentId: 10,
            attachment: []
          }
        ]
      }
    ]
  }
};

// Mock数据 - 维修单详情
const mockRepairDetailResponse = {
  code: '0000',
  message: 'ok',
  data: {
    number: 'wx20250612135920',
    requireStatus: 1,
    requireStatusName: '维修中',
    deviceName: 'JDT0001',
    serialNo: 'LD09RR034959224959',
    stationName: '京东总部2号楼勿动测试中',
    appointmentRepairTime: '2024-05-23 10:09',
    isInfluenceOperation: 1,
    description:
      '我的天啊！他直接摔在这里啊，听里面的的说一半摔示范接吧也听不懂，不知道是不是在这里网络去失了啊，怎么个情况，也听里面的说一半摔示范接吧也听不懂，不知道是不是在这里网络去失了啊，什么他们里面的说一半摔示范接吧也听不懂，不知道是不是在这里网络去失了啊，我也不懂，听里面...我的天啊！他直接摔在这里啊，听里面的的说一半摔示范接吧也听不懂，不知道是不是在这里网络去失了啊，怎么个情况，也听里面的说一半摔示范接吧也听不懂，不知道是不是在这里网络去失了啊，什么他们里面的说一半摔示范接吧也听不懂，不知道是不是在这里网络去失了啊，我也不懂，听里面...',
    pictureList: [
      'https://rover-operation.s3.cn-north-1.jdcloud-oss.com/20220519145309418-%E7%9B%91%E6%8E%A7%E6%9D%83%E9%99%90.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250707T071418Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250707%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=5f34c1bf79ab62a737106e3c9c3468af69d73e1142ce78c7e22759e578f60d7a',
      'https://rover-operation.s3.cn-north-1.jdcloud-oss.com/1.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250707T071639Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250707%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=3656bba127993d6e8e2a616e0a6733ffa6bbb8ff4a1dfe0ec9716fb764a552de',
      'http://rover-operation.s3.cn-north-1.jdcloud-oss.com/h88uu7vg6e.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250707T071724Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250707%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=0dc1235baff6cad1a2c697e6b220911baf0a893c36cb1faa96200214c56f7d8f'
    ],
    video:
      'http://rover-operation.s3.cn-north-1.jdcloud-oss.com/h88uuhlrk8.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250707T071724Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250707%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=531d961f95d305fb1931a9a8712dd4085760bccd721cbad554dde23ee876d796',
    reportName: '刘娜娜',
    reportPhone: '187****4543',
    reportEmail: '<EMAIL>',
    stationBaseAddress: '天津市河东区兴北街道与鲁花街道交叉路口第一百零八号'
  }
};

// Mock数据 - 获取报告人真实电话号码
const mockReportPhoneResponse = {
  code: '0000',
  message: 'ok',
  data: {
    reportPhone: '18700004543'
  }
};

// Mock数据 - 提交评论
const mockPostCommentResponse = {
  code: '0000',
  message: 'ok',
  data: null
};

// Mock数据 - 可见详情
const mockVisibleDetailsResponse = {
  code: '0000',
  message: 'ok',
  data: {
    number: 'wx20250612135920',
    oemId: 1,
    oemName: 'xx车厂',
    oemVisible: 0,
    serviceStationId: 1,
    serviceStationName: 'xxx服务站'
  }
};

// Mock数据 - 设置可见详情
const mockSetVisibleDetailsResponse = {
  code: '0000',
  message: 'ok',
  data: null
};

// Mock数据 - 评论汇总
const mockCommentSummaryResponse = {
  code: '0000',
  data: {
    traceId: 'nb87yu9h89yhoi8y9h98hy',
    reqId: '1750915236'
  },
  message: 'ok'
};

// 轮询mock数据逻辑
let pollStep = 0;
const pollChunkCount = 20; // 轮询次数
const pollContents = [
  `设备信息管理文档总结\n\n1. 文档概述\n本文档详细描述了设备信息管理相关的接口规范，包括接口描述、请求方式、参数说明、示例及响应格式。主要涵盖设备信息的增删改查、硬件管理、站点信息查询等功能。\n---\n`,
  `2. 核心接口列表\n以下是文档中定义的 13 个主要接口，按功能分类总结：\n`,
  `| 接口编号 | 接口名称 | 功能描述 |\n|--------------|--------------|--------------|\n| 1 | 分页查询设备信息列表 | 分页查询设备信息，支持多条件过滤（如车架号、车牌号、设备类型等）。 |\n| 2 | 新建设备信息 | 创建新设备，需提供车牌号、车架号、所属产品、硬件型号等信息。 |\n| 3 | 编辑设备信息 | 更新设备信息，支持修改车牌号、车架号、硬件配置等。 |\n| 4 | 获取设备信息 | 通过设备ID获取详细设备信息，包括硬件配置、站点位置等。 |\n`,
  `| 5 | 根据设备号模糊查询设备信息 | 通过设备名称模糊查询，返回匹配的设备列表。 |\n| 6 | 解绑安卓设备 | 解除设备与安卓设备的绑定关系。 |\n| 7 | 获取车辆API Key | 通过设备ID获取API密钥。 |\n| 8 | 同步车辆信息至OAuth | 将车辆信息同步到OAuth系统。 |\n| 9 | 获取车辆硬件型号信息 | 查询车辆关联的硬件型号详情。 |\n`,
  `| 10 | 判断车辆是否属于同一公司 | 验证多个站点是否属于同一公司。 |\n| 11 | 搜索站点列表 | 根据条件（城市、公司编号等）查询站点信息。 |\n---\n`,
  `3. 关键配置信息\n- 环境参数：\n  - 生产环境：https://jdxgateway.jdl.cn\n  - 联调环境：https://jdxgateway-beta.jdl.cn\n  - 测试环境：https://jdxgateway-test[1-3].jdl.cn\n`,
  `- 请求令牌：\n  - 支持两种方式：Cookie（Rover-Authorization）或 Header（Authorization），均需携带 token。\n---\n`,
  `4. 错误码与响应\n- 响应格式统一包含 code、message 和 data。\n- 错误码未在文档中明确列出，但响应示例中成功状态为 code: "0000"。\n---\n`,
  `5. 特殊说明\n- 业务类型：包括 DISPATCH（配送车）、VENDING（售卖车）、JING_LIN（京麟）、ZHONG_DE（众德）。\n- 产品类型：vehicle（无人车）、robot（机器人）。\n- 硬件管理：接口支持批量绑定硬件型号、物联网卡号等复杂操作。\n---\n`,
  `6. 使用场景\n- 设备管理平台：用于无人车、机器人的设备信息维护。\n- 运营监控系统：依赖接口获取设备状态\n---\n`,
  `接口1: 分页查询设备信息列表\n- 支持多条件过滤，如车架号、车牌号、设备类型等。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口2: 新建设备信息\n- 创建新设备，需提供车牌号、车架号、所属产品、硬件型号等信息。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口3: 编辑设备信息\n- 更新设备信息，支持修改车牌号、车架号、硬件配置等。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口4: 获取设备信息\n- 通过设备ID获取详细设备信息，包括硬件配置、站点位置等。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口5: 根据设备号模糊查询设备信息\n- 通过设备名称模糊查询，返回匹配的设备列表。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口6: 解绑安卓设备\n- 解除设备与安卓设备的绑定关系。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口7: 获取车辆API Key\n- 通过设备ID获取API密钥。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口8: 同步车辆信息至OAuth\n- 将车辆信息同步到OAuth系统。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口9: 获取车辆硬件型号信息\n- 查询车辆关联的硬件型号详情。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口10: 判断车辆是否属于同一公司\n- 验证多个站点是否属于同一公司。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`,
  `接口11: 搜索站点列表\n- 根据条件（城市、公司编号等）查询站点信息。\n- 请求方式：POST\n- 响应格式：{ code, message, data }\n`
];
const pollFull = pollContents.join('');

export const clearPollStep = () => {
  pollStep = 0; // 重置轮询步骤
};

function getMockCommentSummaryResponseData() {
  if (pollStep < pollContents.length) {
    const response = pollContents[pollStep];
    const responseAll = pollContents.slice(0, pollStep + 1).join('');
    pollStep++;
    return {
      code: '0000',
      data: {
        traceId: 'nb87yu9h89yhoi8y9h98hy',
        reqId: '1750915236',
        response,
        responseAll,
        finished: false
      },
      message: 'ok'
    };
  } else {
    return {
      code: '0000',
      data: {
        traceId: 'nb87yu9h89yhoi8y9h98hy',
        reqId: '1750915236',
        response: '',
        responseAll: pollFull,
        finished: true
      },
      message: 'ok'
    };
  }
}

// Mock数据 - 获取通知用户列表（京东受理人）
const mockNotifyUserListResponse = {
  code: '0000',
  message: 'ok',
  data: [
    {
      userName: 'jd_user_001',
      realName: '张三',
      erp: 'zhangsan',
      email: '<EMAIL>'
    }
  ]
};

// Mock数据 - OEM用户列表（车厂）
const mockOemUserListResponse = {
  code: '0000',
  message: 'ok',
  data: [
    {
      userName: 'oem_user_001',
      realName: '张工程师',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_002',
      realName: '李技术总监',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_003',
      realName: '王项目经理',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_004',
      realName: '赵质量主管',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_005',
      realName: '钱研发经理',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_006',
      realName: '孙测试工程师',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_007',
      realName: '周产品经理',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_008',
      realName: '吴系统架构师',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_009',
      realName: '郑硬件工程师',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_010',
      realName: '王软件工程师',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_011',
      realName: '冯算法工程师',
      email: '<EMAIL>'
    },
    {
      userName: 'oem_user_012',
      realName: '陈运维工程师',
      email: '<EMAIL>'
    }
  ]
};

// Mock数据 - 服务站用户列表
const mockStationUserListResponse = {
  code: '0000',
  message: 'ok',
  data: [
    {
      userName: 'station_user_001',
      realName: '张站长',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_002',
      realName: '李维修主管',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_003',
      realName: '王技师',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_004',
      realName: '赵检测员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_005',
      realName: '钱客服专员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_006',
      realName: '孙配件管理员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_007',
      realName: '周质检员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_008',
      realName: '吴调度员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_009',
      realName: '郑接待员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_010',
      realName: '王安全员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_011',
      realName: '冯培训师',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_012',
      realName: '陈区域经理',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_013',
      realName: '褚运营专员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_014',
      realName: '卫数据分析师',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_015',
      realName: '蒋流程优化师',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_016',
      realName: '沈设备维护员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_017',
      realName: '韩库存管理员',
      email: '<EMAIL>'
    },
    {
      userName: 'station_user_018',
      realName: '杨现场主管',
      email: '<EMAIL>'
    }
  ]
};

// Mock数据 - 发送通知响应
const mockSendNotifyResponse = {
  code: '0000',
  message: 'ok',
  data: null
};

// Mock数据 - 操作日志列表
const mockOperationLogListResponse = {
  code: '0000',
  message: 'ok',
  data: {
    list: [
      {
        id: 1,
        type: 'NEW',
        typeName: '报修',
        modifyUserName: '刘娜娜',
        modifyErp: 'liunana',
        modifyTime: '2025-07-10 09:30:00',
        isCurrent: true,
        detailInfo: {
          description: '用户报修，雷达罩脱落影响正常运营',
          attachments: ['image1.jpg', 'image2.jpg']
        }
      },
      {
        id: 2,
        type: 'ACCEPTED',
        typeName: '受理',
        modifyUserName: '张技师',
        modifyErp: 'zhangjishi',
        modifyTime: '2025-07-10 10:15:00',
        isCurrent: false,
        detailInfo: {
          acceptanceNote: '已安排技术人员前往现场检查',
          estimatedTime: '2小时内到达'
        }
      },
      {
        id: 3,
        type: 'COMPLETE_REQUIRE',
        typeName: '跟进-维修完成',
        modifyUserName: '王师傅',
        modifyErp: 'wangshifu',
        modifyTime: '2025-07-10 14:30:00',
        isCurrent: false,
        detailInfo: {
          repairResult: '雷达罩重新固定，系统调试完成',
          testResult: '设备运行正常',
          usedParts: ['雷达罩固定螺丝', '密封胶条']
        }
      },
      {
        id: 4,
        type: 'AFFIRM',
        typeName: '跟进-验收通过',
        modifyUserName: '李主管',
        modifyErp: 'lizhuguan',
        modifyTime: '2025-07-10 16:00:00',
        isCurrent: false,
        detailInfo: {
          inspectionResult: '设备功能正常，外观完好',
          qualityScore: 95,
          remarks: '维修质量良好，可投入使用'
        }
      },
      {
        id: 5,
        type: 'CAN_NOT_ACCEPT',
        typeName: '不受理',
        modifyUserName: null,
        modifyErp: null,
        modifyTime: '2025-07-09 18:20:00',
        isCurrent: false,
        detailInfo: {
          reason: '设备超出保修期，需用户自费维修',
          suggestion: '建议联系原厂技术支持'
        }
      },
      {
        id: 6,
        type: 'REJECTED',
        typeName: '跟进-验收不通过',
        modifyUserName: '赵检验员',
        modifyErp: undefined,
        modifyTime: '2025-07-09 11:45:00',
        isCurrent: false,
        detailInfo: {
          rejectionReason: '雷达精度未达到标准要求',
          requiredActions: ['重新校准雷达参数', '更换高精度传感器'],
          nextInspectionTime: '2025-07-10 09:00:00'
        }
      },
      {
        id: 7,
        type: 'TEMPORARY_STORAGE_REQUIRE',
        typeName: '跟进-维修暂存',
        modifyUserName: '',
        modifyErp: 'temp_storage',
        modifyTime: '2025-07-08 16:30:00',
        isCurrent: false,
        detailInfo: {
          storageReason: '等待配件到货',
          expectedDelivery: '2025-07-09 10:00:00',
          storageLocation: '维修车间A区'
        }
      }
    ]
  }
};

// Mock数据 - 获取受理工单信息详情
const mockAcceptRequireInfoDetailResponse = {
  code: '0000',
  message: 'ok',
  data: {
    number: 'wx111',
    source: '京东物流自营配送中心（北京亦庄分拣中心）',
    requireHardwareTypeIds: [1, 2],
    requireHardwareTypeName: '轮胎传感器模组;激光雷达探测系统;超声波避障传感器',
    maintenanceResultRemark:
      '经过详细检测，发现车辆的激光雷达系统存在硬件故障，主要表现为：1. 激光发射器功率不稳定，导致探测距离缩短；2. 数据处理芯片出现间歇性故障，影响实时数据传输；3. 外壳密封性能下降，可能导致内部元件受潮。建议更换整套激光雷达模组，同时对相关线路进行全面检查和维护。预计维修时间3-5个工作日，需要等待配件到货。',
    needAccessory: 0,
    accessoryOrderNo: '',
    needOldPart: 1,
    oldPartOrderNo: 'OLD20240715001',
    laborCostList: [
      {
        id: 1,
        serviceProject: '激光雷达系统全面检修及数据校准服务',
        cost: 1500.5,
        costType: 'JD',
        remark:
          '包含系统调试、参数配置、软件升级、硬件检测、性能测试等全套服务，确保设备恢复到最佳工作状态'
      },
      {
        id: 2,
        serviceProject: '传感器校准',
        cost: 800.0,
        costType: 'OEM',
        remark: '高精度传感器校准服务'
      },
      {
        id: 3,
        serviceProject: '',
        cost: 0,
        costType: '',
        remark: ''
      }
    ],
    serviceCostList: [
      {
        id: 1,
        serviceProject: '专业技术人员上门检测及故障诊断服务（含设备携带）',
        cost: 200.0,
        costType: 'SERVICE_STATION',
        remark:
          '包含交通费用、人工费、设备使用费，服务范围覆盖市区及周边50公里内区域，提供详细检测报告'
      },
      {
        id: 2,
        serviceProject: '紧急维修服务',
        cost: 500.0,
        costType: 'JD',
        remark: '24小时紧急响应'
      },
      {
        id: 3,
        serviceProject: null,
        cost: null,
        costType: 'UNKNOWN_TYPE',
        remark: null
      }
    ],
    otherCostList: [
      {
        id: 1,
        serviceProject: '大型设备专用运输及包装服务',
        cost: 150.0,
        costType: 'OEM',
        remark:
          '往返运输费用，包含专业包装材料、保险费用、装卸费，确保设备运输过程中的安全性，提供全程跟踪服务'
      },
      {
        id: 2,
        serviceProject: '临时存储费',
        cost: 80.0,
        costType: 'SERVICE_STATION',
        remark: '设备临时存储7天'
      },
      {
        id: 3,
        serviceProject:
          '超长文本测试项目名称，用于测试在移动端显示时的文本换行和布局效果',
        cost: 999999.99,
        costType: 'OTHER',
        remark:
          '这是一个超长的备注信息，用于测试在移动端界面中长文本的显示效果。内容包括：详细的服务说明、注意事项、免责声明、联系方式等信息。测试文本应该能够正确换行，不会影响页面布局，同时保持良好的可读性。'
      }
    ],
    requireHardwareModelInfoList: [
      {
        id: 1,
        isOfVehicleType: 1,
        oldHardwareModelId: 1,
        oldHardwareModelName:
          '激光雷达探测系统 V1.0 高精度版本（含数据处理模块）',
        newHardwareModelId: 2,
        newHardwareModelName:
          '激光雷达探测系统 V2.0 企业级版本（含AI算法优化模块）',
        newHardwareModelNumber: 'LR-V2-ENT-12345-BATCH-001',
        newHardwareModelCount: 1,
        newHardwareModelCost: 8500.5,
        costType: 'OEM',
        remark:
          '升级版本，性能提升30%，增加了AI智能识别功能，支持复杂环境下的精准探测，质保期延长至3年，包含免费软件升级服务'
      },
      {
        id: 2,
        isOfVehicleType: 0,
        oldHardwareModelId: 3,
        oldHardwareModelName: '',
        newHardwareModelId: 4,
        newHardwareModelName: '高清摄像头模组',
        newHardwareModelNumber: 'CAM-HD-67890',
        newHardwareModelCount: 2,
        newHardwareModelCost: 1200.0,
        costType: 'JD',
        remark: '双摄像头配置，提升识别精度'
      },
      {
        id: 3,
        isOfVehicleType: 1,
        oldHardwareModelId: null,
        oldHardwareModelName: null,
        newHardwareModelId: 5,
        newHardwareModelName: '超声波避障传感器阵列（12通道专业版）',
        newHardwareModelNumber: 'US-SENSOR-PRO-11111-ARRAY-12CH',
        newHardwareModelCount: 4,
        newHardwareModelCost: 600.0,
        costType: 'SERVICE_STATION',
        remark:
          '新增传感器，增强避障能力，支持360度全方位检测，响应时间<10ms，防水等级IP67'
      },
      {
        id: 4,
        isOfVehicleType: 1,
        oldHardwareModelId: 6,
        oldHardwareModelName: '控制器模块（标准版）',
        newHardwareModelId: 7,
        newHardwareModelName: '',
        newHardwareModelNumber: '',
        newHardwareModelCount: 0,
        newHardwareModelCost: 0,
        costType: '',
        remark: ''
      }
    ]
  }
};

class TechMaintenanceApi {
  public async getServiceStationRequireList() {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/get_service_station_require_list',
      method: 'POST',
      data: {}
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockRepairListResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async getRequireCommentList(number: string) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/get_require_comment_list',
      method: 'POST',
      data: {
        number
      }
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockCommentListResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async getRequireInfoDetail(number: string) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/require_info_get_detail',
      method: 'POST',
      data: {
        number
      }
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockRepairDetailResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async getRequireReportPhone(number: string) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/get_require_report_phone',
      method: 'POST',
      data: {
        number
      }
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockReportPhoneResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async postComment(params: {
    number: string;
    replyToCommentId: number;
    content: string;
    attachment: Array<{ type: string; fileKey: string; bucketName: string }>;
  }) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/post_comment',
      method: 'POST',
      data: params
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockPostCommentResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async getVisibleDetails(number: string) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/get_visible_details',
      method: 'POST',
      data: { number }
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockVisibleDetailsResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async setVisibleDetails(params: {
    number: string;
    type: string;
    oemVisible: number;
  }) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/set_visible_details',
      method: 'POST',
      data: params
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockSetVisibleDetailsResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async commentSummaryRequest(number: string) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/comment_summary_request',
      method: 'POST',
      data: { number }
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockCommentSummaryResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async commentSummaryResponse(params: {
    number: string;
    traceId: string;
    reqId: string;
  }) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/comment_summary_response',
      method: 'POST',
      data: params
    };
    // 返回mock数据，用于测试
    return Promise.resolve(getMockCommentSummaryResponseData());
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async getNotifyUserList(params: {
    number: string;
    type: 'JD' | 'OEM' | 'PROGRAM_SERVICE_STATION';
  }) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/get_notify_user_list',
      method: 'POST',
      data: params
    };
    // 返回mock数据，用于测试
    let mockResponse;
    switch (params.type) {
      case 'JD':
        mockResponse = mockNotifyUserListResponse;
        break;
      case 'OEM':
        mockResponse = mockOemUserListResponse;
        break;
      case 'PROGRAM_SERVICE_STATION':
        mockResponse = mockStationUserListResponse;
        break;
      default:
        mockResponse = mockNotifyUserListResponse;
    }
    return Promise.resolve(mockResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async sendNotify(params: {
    oemUserNameList: string[];
    serviceStationUserNameList: string[];
    vehicleReportUserName: string;
    number: string;
  }) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/send_notify',
      method: 'POST',
      data: params
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockSendNotifyResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async getAcceptRequireInfoDetail(number: string) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/mobile/require/get_accept_require_info_detail',
      method: 'POST',
      data: {
        number
      }
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockAcceptRequireInfoDetailResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }

  public async getRequireLogList(number: string) {
    const requestOptions: RequestOptions = {
      url: '/k2/management/require/get_require_log_list',
      method: 'POST',
      data: {
        number
      }
    };
    // 返回mock数据，用于测试
    return Promise.resolve(mockOperationLogListResponse);
    // 实际接口调用
    // return doRequest(requestOptions);
  }
}

export default TechMaintenanceApi;
