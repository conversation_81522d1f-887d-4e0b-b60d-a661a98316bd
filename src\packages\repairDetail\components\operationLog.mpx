<template>
  <view class="operate-log" style="background: rgba(245, 245, 245, 0.5)">
    <view class="log-content">
      <timeline timelineData="{{timelineData}}">
        <template slot="content" slot-scope="props">
          <view class="log-detail">
            <view class="time-and-action">
              <view class="log-time">{{ props.item.modifyTime }}</view>
              <view
                wx:if="{{props.item.hasDetail}}"
                class="detail-button"
                bindtap="onShowDetail"
                data-item="{{props.item}}"
              >
                <text class="detail-text">详情</text>
                <view class="arrow-right"></view>
              </view>
            </view>
          </view>
        </template>
      </timeline>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { OperationLogTypeNameMap } from 'shared/utils/constant';

  interface OperationLogItem {
    id: number;
    type: string;
    typeName: string;
    modifyUserName: string | null | undefined;
    modifyErp: string | null | undefined;
    modifyTime: string;
    isCurrent?: boolean;
    detailInfo?: any;
  }

  const app = getApp();
  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      operationLogList: [] as OperationLogItem[],
      loading: false
    },
    computed: {
      timelineData() {
        return this.operationLogList.map((item: OperationLogItem) => {
          const userInfo = this.formatUserInfo(
            item.modifyUserName,
            item.modifyErp
          );
          return {
            id: item.id,
            title: item.typeName,
            description: userInfo,
            isCurrent: item.isCurrent,
            modifyTime: item.modifyTime,
            hasDetail: !!item.detailInfo,
            originalItem: item
          };
        });
      }
    },

    methods: {
      formatUserInfo(
        modifyUserName: string | null | undefined,
        modifyErp: string | null | undefined
      ) {
        if (!modifyUserName && !modifyErp) {
          return '';
        }
        if (!modifyUserName) {
          return modifyErp ? `(${modifyErp})` : '';
        }
        if (!modifyErp) {
          return modifyUserName;
        }
        return `${modifyUserName}(${modifyErp})`;
      },

      async loadOperationLog() {
        if (!this.orderNumber) {
          return;
        }

        try {
          this.loading = true;
          const response = await this.techMaintenanceApi.getRequireLogList(
            this.orderNumber
          );

          if (response.code === '0000') {
            this.operationLogList = response.data.list || [];
          } else {
            console.error('获取操作日志失败:', response.message);
          }
        } catch (error) {
          console.error('获取操作日志异常:', error);
        } finally {
          this.loading = false;
        }
      },

      onShowDetail(event: any) {
        const item = event.currentTarget.dataset.item;
        console.log('查看详情:', item);
        // TODO: 实现详情弹窗或跳转
      }
    },

    lifetimes: {
      created() {},
      attached() {
        this.loadOperationLog();
      },
      detached() {},
      ready() {}
    },

    observers: {
      orderNumber(newVal: string) {
        if (newVal) {
          this.loadOperationLog();
        }
      }
    }
  });
</script>

<style lang="scss">
  .operate-log {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 32rpx 24rpx;
  }

  .log-content {
    width: 100%;
    height: 100%;
  }

  .log-detail {
    width: 100%;
  }

  .time-and-action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .log-time {
    font-size: 24rpx;
    color: #999;
    line-height: 32rpx;
  }

  .detail-button {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .detail-text {
    font-size: 24rpx;
    color: #fa3a28;
    line-height: 32rpx;
    margin-right: 8rpx;
  }

  .arrow-right {
    width: 0;
    height: 0;
    border-left: 8rpx solid #fa3a28;
    border-top: 6rpx solid transparent;
    border-bottom: 6rpx solid transparent;
    transform: rotate(0deg);
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "timeline": "shared/ui/timeline.mpx"
    }
  }
</script>