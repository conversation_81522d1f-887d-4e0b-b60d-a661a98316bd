<template>
  <view class="operate-log" style="background: rgba(245, 245, 245, 0.5)">
    <view class="log-content">
      <timeline
        timelineData="{{timelineData}}"
        bind:actiontap="onActionTap"
      ></timeline>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { OperationLogTypeNameMap } from 'shared/utils/constant';

  interface OperationLogItem {
    id: number;
    type: string;
    typeName: string;
    modifyUserName: string | null | undefined;
    modifyErp: string | null | undefined;
    modifyTime: string;
    isCurrent?: boolean;
    detailInfo?: any;
  }

  const app = getApp();
  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      operationLogList: [] as OperationLogItem[],
      loading: false
    },
    computed: {
      timelineData() {
        return this.operationLogList.map((item: OperationLogItem) => {
          const userInfo = this.formatUserInfo(
            item.modifyUserName,
            item.modifyErp
          );

          // 构建actions数组
          const actions = [];
          if (item.detailInfo) {
            actions.push({
              type: 'detail',
              text: '详情',
              color: '#fa3a28',
              showArrow: true,
              originalItem: item
            });
          }

          return {
            id: item.id,
            title: item.typeName,
            description: userInfo,
            isCurrent: item.isCurrent,
            time: item.modifyTime,
            actions: actions,
            originalItem: item
          };
        });
      }
    },

    methods: {
      formatUserInfo(
        modifyUserName: string | null | undefined,
        modifyErp: string | null | undefined
      ) {
        if (!modifyUserName && !modifyErp) {
          return '';
        }
        if (!modifyUserName) {
          return modifyErp ? `(${modifyErp})` : '';
        }
        if (!modifyErp) {
          return modifyUserName;
        }
        return `${modifyUserName}(${modifyErp})`;
      },

      async loadOperationLog() {
        if (!this.orderNumber) {
          return;
        }

        try {
          this.loading = true;
          const response = await this.techMaintenanceApi.getRequireLogList(
            this.orderNumber
          );

          if (response.code === '0000') {
            this.operationLogList = response.data.list || [];
          } else {
            console.error('获取操作日志失败:', response.message);
          }
        } catch (error) {
          console.error('获取操作日志异常:', error);
        } finally {
          this.loading = false;
        }
      },

      onActionTap(event: any) {
        const { action, item } = event.detail;
        if (action.type === 'detail') {
          this.onShowDetail(action.originalItem);
        }
      },

      onShowDetail(item: OperationLogItem) {
        console.log('查看详情:', item);
        // TODO: 实现详情弹窗或跳转
      }
    },

    lifetimes: {
      created() {},
      attached() {
        this.loadOperationLog();
      },
      detached() {},
      ready() {}
    },

    observers: {
      orderNumber(newVal: string) {
        if (newVal) {
          this.loadOperationLog();
        }
      }
    }
  });
</script>

<style lang="scss">
  .operate-log {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 32rpx 24rpx;
  }

  .log-content {
    width: 100%;
    height: 100%;
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "timeline": "shared/ui/timeline.mpx"
    }
  }
</script>