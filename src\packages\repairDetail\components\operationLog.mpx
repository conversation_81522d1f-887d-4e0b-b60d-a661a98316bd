<template>
  <view class="operate-log" style="background: rgba(245, 245, 245, 0.5)">
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { CostTypeNameMap } from 'shared/utils/constant';

  const app = getApp();
  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      maintainData: {} as any
    },
    computed: {},

    methods: {},

    lifetimes: {
      created() {},
      attached() {},
      detached() {},
      ready() {}
    }
  });
</script>

<style lang="scss">
  .operate-log {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {}
  }
</script>