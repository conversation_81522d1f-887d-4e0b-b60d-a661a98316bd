<template>
  <view class="operate-log" style="background: rgba(245, 245, 245, 0.5)">
    <view class="log-content">
      <view class="timeline-container">
        <timeline
          wx:for="{{timelineData}}"
          wx:key="id"
          wx:for-index="index"
          title="{{item.title}}"
          description="{{item.description}}"
          isCurrent="{{item.isCurrent}}"
          isLast="{{index === timelineData.length - 1}}"
        >
          <view class="log-detail">
            <view class="time-and-action">
              <view class="log-time">{{ item.modifyTime }}</view>
              <view
                wx:if="{{item.hasDetail}}"
                class="detail-button"
                bindtap="onShowDetail"
                data-item="{{item}}"
              >
                <text class="detail-text">详情</text>
                <view class="arrow-right"></view>
              </view>
            </view>
          </view>
        </timeline>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { OperationLogType } from 'shared/utils/constant';

  interface OperationLogItem {
    id: number;
    type: string;
    typeName: string;
    modifyUserName: string | null | undefined;
    modifyErp: string | null | undefined;
    modifyTime: string;
    isCurrent?: boolean;
    detailInfo?: any;
  }

  const app = getApp();
  createComponent({
    properties: {
      orderNumber: {
        type: String,
        value: ''
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      operationLogList: [] as OperationLogItem[],
      loading: false
    },
    computed: {
      timelineData() {
        return this.operationLogList.map((item: OperationLogItem, index) => {
          const userInfo = this.formatUserInfo(
            item.modifyUserName,
            item.modifyErp
          );
          const isCurrent =
            index === this.operationLogList.length - 1 ? true : false;
          let hasDetail = true;
          if (item.type === OperationLogType.NEW) {
            hasDetail = false;
          }
          return {
            id: item.id,
            title: item.typeName,
            description: userInfo,
            isCurrent,
            modifyTime: item.modifyTime,
            hasDetail,
            originalItem: item
          };
        });
      }
    },

    methods: {
      formatUserInfo(
        modifyUserName: string | null | undefined,
        modifyErp: string | null | undefined
      ) {
        if (!modifyUserName && !modifyErp) {
          return '-';
        }
        if (!modifyUserName) {
          return modifyErp ? `(${modifyErp})` : '';
        }
        if (!modifyErp) {
          return modifyUserName;
        }
        return `${modifyUserName}(${modifyErp})`;
      },

      async loadOperationLog() {
        if (!this.orderNumber) {
          return;
        }
        try {
          this.loading = true;
          const response = await this.techMaintenanceApi.getRequireLogList(
            this.orderNumber
          );

          if (response.code === HTTPSTATUSCODE.Success) {
            this.operationLogList = response.data.list || [];
          } else {
            console.error('获取操作日志失败:', response.message);
          }
        } catch (error) {
          console.error('获取操作日志异常:', error);
        } finally {
          this.loading = false;
        }
      },

      onShowDetail(event: any) {
        const item = event.currentTarget.dataset.item;
        console.log('查看详情:', item);
        // TODO: 实现详情弹窗或跳转
      }
    },

    lifetimes: {
      created() {},
      attached() {
        this.loadOperationLog();
      },
      detached() {},
      ready() {}
    },

    observers: {
      orderNumber(newVal: string) {
        if (newVal) {
          this.loadOperationLog();
        }
      }
    }
  });
</script>

<style lang="scss">
  .operate-log {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 32rpx 24rpx;
  }

  .log-content {
    width: 100%;
    height: 100%;
  }

  .timeline-container {
    width: 100%;
    padding: 0;
  }

  .log-detail {
    width: 100%;
  }

  .time-and-action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .log-time {
    font-size: 24rpx;
    color: #999;
    line-height: 32rpx;
  }

  .detail-button {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .detail-text {
    font-size: 28rpx;
    color: #fa3a28;
    line-height: 32rpx;
    margin-right: 8rpx;
  }

  .arrow-right {
    width: 12rpx;
    height: 12rpx;
    border-top: 2rpx solid #fa3a28;
    border-right: 2rpx solid #fa3a28;
    transform: rotate(45deg);
    transition:
      transform 0.3s ease,
      border-color 0.3s ease;
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "timeline": "shared/ui/timeline.mpx"
    }
  }
</script>